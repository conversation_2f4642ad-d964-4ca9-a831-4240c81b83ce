<?php

namespace Modules\PartnerManagement\Console\Commands;

use Illuminate\Console\Command;
use Modules\PartnerManagement\Models\PartnerLicense;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateExpiredLicensesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'partner:update-expired-licenses {--dry-run : Show what would be updated without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update expired partner licenses to status = 3 (EXPIRED)';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting expired licenses update process...');
        
        $isDryRun = $this->option('dry-run');
        
        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        try {
            // Find licenses that are expired but not marked as expired
            $expiredLicenses = PartnerLicense::where('expired_at', '<', Carbon::now())
                ->where('status', '!=', PartnerLicense::STATUS_EXPIRED)
                ->whereNotNull('expired_at')
                ->get();

            if ($expiredLicenses->isEmpty()) {
                $this->info('No expired licenses found to update.');
                return 0;
            }

            $this->info("Found {$expiredLicenses->count()} expired licenses to update:");

            // Display licenses that will be updated
            $headers = ['ID', 'Code', 'Current Status', 'Expired At', 'Company ID', 'Project Account'];
            $rows = [];

            foreach ($expiredLicenses as $license) {
                $rows[] = [
                    $license->id,
                    $license->code,
                    $license->status_label,
                    $license->expired_at_formatted,
                    $license->company_id ?? 'N/A',
                    $license->project_account ?? 'N/A'
                ];
            }

            $this->table($headers, $rows);

            if ($isDryRun) {
                $this->warn('DRY RUN: The above licenses would be updated to EXPIRED status.');
                return 0;
            }

            // Confirm before proceeding
            // if (!$this->confirm('Do you want to proceed with updating these licenses to EXPIRED status?')) {
            //     $this->info('Operation cancelled.');
            //     return 0;
            // }

            // Update licenses in batches
            $updatedCount = 0;
            $batchSize = 100;

            DB::beginTransaction();

            try {
                foreach ($expiredLicenses->chunk($batchSize) as $batch) {
                    $licenseIds = $batch->pluck('id')->toArray();
                    
                    // Update status to EXPIRED
                    $updated = PartnerLicense::whereIn('id', $licenseIds)
                        ->update([
                            'status' => PartnerLicense::STATUS_EXPIRED,
                            'updated_at' => Carbon::now()
                        ]);
                    
                    $updatedCount += $updated;
                    
                    // Log each updated license
                    foreach ($batch as $license) {
                        Log::info('License expired and updated', [
                            'license_id' => $license->id,
                            'license_code' => $license->code,
                            'previous_status' => $license->status,
                            'new_status' => PartnerLicense::STATUS_EXPIRED,
                            'expired_at' => $license->expired_at,
                            'company_id' => $license->company_id,
                            'project_account' => $license->project_account,
                            'updated_by' => 'system_command'
                        ]);
                    }
                }

                DB::commit();

                $this->info("Successfully updated {$updatedCount} licenses to EXPIRED status.");
                
                // Log summary
                Log::info('Expired licenses update completed', [
                    'total_updated' => $updatedCount,
                    'execution_time' => Carbon::now(),
                    'command' => 'partner:update-expired-licenses'
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            $this->error('Error updating expired licenses: ' . $e->getMessage());
            Log::error('Failed to update expired licenses', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }

        return 0;
    }
}
